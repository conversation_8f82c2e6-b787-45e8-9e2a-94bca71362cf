import React, { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { Save, Loader2, CheckCircle, Volume2, Play, Pause, Plus, Trash2, Image, RotateCcw } from 'lucide-react'
import { cn } from '../../utils/cn'

// Custom Audio Player Component
interface AudioPlayerProps {
  src: string
  title?: string
  className?: string
}

const AudioPlayer: React.FC<AudioPlayerProps> = React.memo(({ src, title, className }) => {
  const audioRef = useRef<HTMLAudioElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)

  const togglePlay = (e: React.MouseEvent) => {
    // Prevent event bubbling to avoid triggering parent handlers
    e.preventDefault()
    e.stopPropagation()

    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause()
      } else {
        audioRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime)
    }
  }

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration)
    }
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Prevent event bubbling
    e.stopPropagation()

    const time = parseFloat(e.target.value)
    if (audioRef.current) {
      audioRef.current.currentTime = time
      setCurrentTime(time)
    }
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  return (
    <div
      className={cn('flex items-center gap-3 p-3 bg-slate-100 dark:bg-slate-800 rounded-lg', className)}
      onClick={(e) => e.stopPropagation()} // Prevent any click events from bubbling up
    >
      <audio
        ref={audioRef}
        src={src}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={() => setIsPlaying(false)}
        onPlay={(e) => e.stopPropagation()}
        onPause={(e) => e.stopPropagation()}
      />

      <button
        onClick={togglePlay}
        className="p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-colors"
      >
        {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
      </button>

      <div className="flex-1 space-y-1">
        {title && <div className="text-xs font-medium text-foreground">{title}</div>}
        <div className="flex items-center gap-2">
          <span className="text-xs text-muted-foreground">{formatTime(currentTime)}</span>
          <input
            type="range"
            min="0"
            max={duration || 0}
            value={currentTime}
            onChange={handleSeek}
            onClick={(e) => e.stopPropagation()}
            className="flex-1 h-1 bg-slate-300 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer"
          />
          <span className="text-xs text-muted-foreground">{formatTime(duration)}</span>
        </div>
      </div>

      <Volume2 className="w-4 h-4 text-blue-500" />
    </div>
  )
})

interface QuestionData {
  id: string
  type: string
  title: string
  question: {
    text: string
    translated_text: string
    options: Record<string, string>
    options_en: Record<string, string>
    correct_answer_index: number
    answer_hint?: string
    audio_metadata?: any
    image_metadata?: any
    options_metadata?: Record<string, any>
    metadata?: any
  }
  correct_answer: {
    text?: string
    index?: number
    explanation?: string
    type?: string
    value?: string | string[]
  }
}

interface QuestionEditFormProps {
  questionData: QuestionData
  onSave: (taskId: string, updatedData: any) => Promise<void>
  onCancel: () => void
  onSaveSuccess?: () => void
  saving?: boolean
}

/**
 * Dynamic form component for editing question data
 * Handles text, translated text, options, and answer hints
 */
const QuestionEditForm: React.FC<QuestionEditFormProps> = ({
  questionData,
  onSave,
  onCancel,
  onSaveSuccess,
  saving = false
}) => {
  // Determine answer type from existing data
  const getAnswerTypeFromData = () => {
    if (questionData.correct_answer?.type === 'multiple_choice' || Array.isArray(questionData.correct_answer?.value)) {
      return 'multiple'
    }
    return 'single'
  }

  // Parse existing correct answer value
  const parseCorrectAnswerValue = () => {
    const value = questionData.correct_answer?.value
    const optionKeys = Object.keys(questionData.question.options)

    console.log('Parsing correct answer value:', {
      value,
      optionKeys,
      questionData: questionData.correct_answer
    })

    if (Array.isArray(value)) {
      // Convert option letters to indices
      const indices = value.map(letter => optionKeys.indexOf(letter)).filter(index => index >= 0)
      console.log('Array value parsed to indices:', indices)
      return indices
    } else if (typeof value === 'string') {
      if (value.includes(',')) {
        // Handle comma-separated string
        const indices = value.split(',').map(letter => optionKeys.indexOf(letter.trim())).filter(index => index >= 0)
        console.log('Comma-separated value parsed to indices:', indices)
        return indices
      } else {
        // Single value - could be letter or index
        const letterIndex = optionKeys.indexOf(value)
        const result = letterIndex >= 0 ? [letterIndex] : [parseInt(value) || 0]
        console.log('Single value parsed:', { value, letterIndex, result })
        return result
      }
    }

    // Fallback to correct_answer_index
    const fallback = [questionData.question.correct_answer_index || 0]
    console.log('Using fallback value:', fallback)
    return fallback
  }

  const answerType = getAnswerTypeFromData()
  const initialSelectedAnswers = parseCorrectAnswerValue()

  const [formData, setFormData] = useState({
    text: questionData.question.text || '',
    translated_text: questionData.question.translated_text || '',
    options: { ...questionData.question.options },
    options_en: { ...questionData.question.options_en },
    answer_hint: questionData.question.answer_hint || '',
    correct_answer_index: questionData.question.correct_answer_index || 0,
    correct_answer_type: answerType
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showSuccess, setShowSuccess] = useState(false)
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>(initialSelectedAnswers)
  const [originalData, setOriginalData] = useState({
    text: questionData.question.text || '',
    translated_text: questionData.question.translated_text || '',
    options: { ...questionData.question.options },
    options_en: { ...questionData.question.options_en },
    answer_hint: questionData.question.answer_hint || '',
    selectedAnswers: initialSelectedAnswers
  })

  // Update form data when questionData ID changes (not on every data refresh)
  useEffect(() => {
    console.log('QuestionData ID changed:', {
      id: questionData.id,
      correctAnswer: questionData.correct_answer,
      options: questionData.question.options
    })

    // Re-calculate everything from fresh data
    const newAnswerType = getAnswerTypeFromData()
    const newSelectedAnswers = parseCorrectAnswerValue()

    console.log('Updating form with:', {
      answerType: newAnswerType,
      selectedAnswers: newSelectedAnswers,
      originalValue: questionData.correct_answer?.value
    })

    const newFormData = {
      text: questionData.question.text || '',
      translated_text: questionData.question.translated_text || '',
      options: { ...questionData.question.options },
      options_en: { ...questionData.question.options_en },
      answer_hint: questionData.question.answer_hint || '',
      correct_answer_index: newSelectedAnswers[0] || 0, // Use the parsed selected answer
      correct_answer_type: newAnswerType
    }

    setFormData(newFormData)
    setSelectedAnswers(newSelectedAnswers)

    // Store original data for change detection
    setOriginalData({
      text: questionData.question.text || '',
      translated_text: questionData.question.translated_text || '',
      options: { ...questionData.question.options },
      options_en: { ...questionData.question.options_en },
      answer_hint: questionData.question.answer_hint || '',
      selectedAnswers: newSelectedAnswers
    })

    setErrors({}) // Clear any existing errors
    setShowSuccess(false) // Clear success message
  }, [questionData.id]) // Only watch the question ID, not the entire object

  // Check if there are changes from original data
  const hasChanges = () => {
    // Check text fields (trim to avoid false positives from whitespace)
    if (formData.text.trim() !== originalData.text.trim()) {
      console.log('Text changed:', formData.text, '!==', originalData.text)
      return true
    }
    if (formData.translated_text.trim() !== originalData.translated_text.trim()) {
      console.log('Translated text changed:', formData.translated_text, '!==', originalData.translated_text)
      return true
    }
    if (formData.answer_hint.trim() !== originalData.answer_hint.trim()) {
      console.log('Answer hint changed:', formData.answer_hint, '!==', originalData.answer_hint)
      return true
    }

    // Check options
    const originalOptions = originalData.options
    const currentOptions = formData.options
    const optionKeys = Object.keys(originalOptions)

    for (const key of optionKeys) {
      if (originalOptions[key] !== currentOptions[key]) {
        console.log(`Option ${key} changed:`, currentOptions[key], '!==', originalOptions[key])
        return true
      }
    }

    // Check English options
    const originalOptionsEn = originalData.options_en
    const currentOptionsEn = formData.options_en
    const optionKeysEn = Object.keys(originalOptionsEn)

    for (const key of optionKeysEn) {
      if (originalOptionsEn[key] !== currentOptionsEn[key]) {
        console.log(`English option ${key} changed:`, currentOptionsEn[key], '!==', originalOptionsEn[key])
        return true
      }
    }

    // Check selected answers
    if (selectedAnswers.length !== originalData.selectedAnswers.length) {
      console.log('Selected answers length changed:', selectedAnswers.length, '!==', originalData.selectedAnswers.length)
      return true
    }
    for (let i = 0; i < selectedAnswers.length; i++) {
      if (selectedAnswers[i] !== originalData.selectedAnswers[i]) {
        console.log(`Selected answer ${i} changed:`, selectedAnswers[i], '!==', originalData.selectedAnswers[i])
        return true
      }
    }

    console.log('No changes detected')
    return false
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.text.trim()) {
      newErrors.text = 'Question text is required'
    }

    if (!formData.translated_text.trim()) {
      newErrors.translated_text = 'Translated text is required'
    }

    // Validate options
    const optionKeys = Object.keys(formData.options)
    if (optionKeys.length === 0) {
      newErrors.options = 'At least one option is required'
    } else {
      optionKeys.forEach(key => {
        if (!formData.options[key]?.trim()) {
          newErrors[`option_${key}`] = `Option ${key.toUpperCase()} is required`
        }
        // English options are optional - only validate if they exist in original data
        if (originalData.options_en[key] && !formData.options_en[key]?.trim()) {
          newErrors[`option_en_${key}`] = `English option ${key.toUpperCase()} is required`
        }
      })
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    console.log('🚀 handleSubmit called!')
    e.preventDefault()
    console.log('Form submitted, validating...')

    const isValid = validateForm()
    console.log('Form validation result:', isValid, 'Errors:', errors)

    if (!isValid) {
      console.log('Form validation failed, stopping submission')
      return
    }

    console.log('Form is valid, proceeding with save...')

    try {
      const optionKeys = Object.keys(formData.options)

      // Prepare correct answer value in the same format as received
      let correctAnswerValue: string | string[]
      if (formData.correct_answer_type === 'single') {
        // Return the option letter (a, b, c, etc.)
        correctAnswerValue = optionKeys[selectedAnswers[0]] || 'a'
      } else {
        // Return array of option letters, sorted to maintain consistency
        correctAnswerValue = selectedAnswers
          .sort()
          .map(index => optionKeys[index])
          .filter(Boolean)
      }

      console.log('Saving question with correct answer:', {
        type: questionData.correct_answer?.type || (formData.correct_answer_type === 'single' ? 'single_choice' : 'multiple_choice'),
        value: correctAnswerValue,
        selectedAnswers,
        optionKeys
      })

      const updateData = {
        text: formData.text,
        translated_text: formData.translated_text,
        options: formData.options,
        options_en: formData.options_en,
        answer_hint: formData.answer_hint,
        correct_answer: {
          type: questionData.correct_answer?.type || (formData.correct_answer_type === 'single' ? 'single_choice' : 'multiple_choice'),
          value: correctAnswerValue
        },
        regenerate_audio: true
      }

      console.log('About to call onSave with:', { questionId: questionData.id, updateData })
      await onSave(questionData.id, updateData)
      console.log('onSave completed successfully')

      setShowSuccess(true)
      setTimeout(() => setShowSuccess(false), 3000)

      // Trigger refresh callback if provided
      onSaveSuccess?.()
    } catch (error) {
      console.error('Error saving question:', error)
    }
  }

  const handleReset = () => {
    // Reset form to original values
    const newSelectedAnswers = parseCorrectAnswerValue()
    const newAnswerType = getAnswerTypeFromData()

    setFormData({
      text: questionData.question.text || '',
      translated_text: questionData.question.translated_text || '',
      options: { ...questionData.question.options },
      options_en: { ...questionData.question.options_en },
      answer_hint: questionData.question.answer_hint || '',
      correct_answer_index: newSelectedAnswers[0] || 0,
      correct_answer_type: newAnswerType
    })

    setSelectedAnswers(newSelectedAnswers)
    setErrors({})
    setShowSuccess(false)
  }

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handleOptionChange = (optionKey: string, value: string, isEnglish = false) => {
    const field = isEnglish ? 'options_en' : 'options'
    setFormData(prev => ({
      ...prev,
      [field]: {
        ...prev[field],
        [optionKey]: value
      }
    }))

    // Clear error
    const errorKey = isEnglish ? `option_en_${optionKey}` : `option_${optionKey}`
    if (errors[errorKey]) {
      setErrors(prev => ({
        ...prev,
        [errorKey]: ''
      }))
    }
  }

  const addOption = () => {
    const existingKeys = Object.keys(formData.options)
    const nextKey = String.fromCharCode(97 + existingKeys.length) // a, b, c, d, etc.

    setFormData(prev => ({
      ...prev,
      options: {
        ...prev.options,
        [nextKey]: ''
      },
      options_en: {
        ...prev.options_en,
        [nextKey]: ''
      }
    }))
  }

  const removeOption = (optionKey: string) => {
    setFormData(prev => {
      const newOptions = { ...prev.options }
      const newOptionsEn = { ...prev.options_en }
      delete newOptions[optionKey]
      delete newOptionsEn[optionKey]

      return {
        ...prev,
        options: newOptions,
        options_en: newOptionsEn
      }
    })

    // Remove from selected answers if it was selected
    const optionIndex = Object.keys(formData.options).indexOf(optionKey)
    setSelectedAnswers(prev => prev.filter(index => index !== optionIndex))
  }

  const handleAnswerSelection = (index: number) => {
    console.log('Answer selection changed:', { index, currentType: formData.correct_answer_type })

    if (formData.correct_answer_type === 'single') {
      setSelectedAnswers([index])
      setFormData(prev => ({ ...prev, correct_answer_index: index }))
      console.log('Single choice - new selection:', [index])
    } else {
      setSelectedAnswers(prev => {
        const newSelection = prev.includes(index)
          ? prev.filter(i => i !== index)
          : [...prev, index].sort() // Sort to maintain consistent order
        console.log('Multiple choice - new selection:', newSelection)
        return newSelection
      })
    }
  }

  const optionKeys = Object.keys(formData.options)

  // Calculate current value based on selected answers
  const getCurrentValue = () => {
    if (selectedAnswers.length === 0) {
      console.log('getCurrentValue: No selections')
      return ''
    }

    let result: string
    if (formData.correct_answer_type === 'single') {
      // Return the option letter for single choice
      result = optionKeys[selectedAnswers[0]] || ''
      console.log('getCurrentValue (single):', { selectedAnswers, optionKeys, result })
    } else {
      // Return comma-separated letters for multiple choice
      result = selectedAnswers
        .sort()
        .map(index => optionKeys[index])
        .filter(Boolean)
        .join(', ')
      console.log('getCurrentValue (multiple):', { selectedAnswers, optionKeys, result })
    }

    return result
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-slate-900 rounded-xl border border-slate-200 dark:border-slate-700 p-4 space-y-4"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-foreground">
            Edit Question: {questionData.title}
          </h3>
          <p className="text-sm text-muted-foreground">
            Type: {questionData.type}
          </p>
        </div>
        {showSuccess && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="flex items-center gap-2 text-green-600"
          >
            <CheckCircle className="w-5 h-5" />
            <span className="text-sm font-medium">Saved successfully!</span>
          </motion.div>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Media Section */}
        {(questionData.question.metadata?.url || questionData.question.image_metadata?.url) && (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl p-6 border border-blue-200/50 dark:border-blue-800/30">
            <div className="flex items-center gap-2 mb-4">
              {questionData.question.metadata?.content_type?.startsWith('audio') || questionData.type === 'speak_word' ? (
                <Volume2 className="w-5 h-5 text-blue-600" />
              ) : (
                <Image className="w-5 h-5 text-blue-600" />
              )}
              <h4 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                {questionData.question.metadata?.content_type?.startsWith('audio') || questionData.type === 'speak_word'
                  ? 'Audio to Speak'
                  : 'Image to Identify'}
              </h4>
            </div>

            <div className="space-y-4">
              {/* Audio Player for audio content */}
              {questionData.question.metadata?.url &&
               (questionData.question.metadata?.content_type?.startsWith('audio') || questionData.type === 'speak_word') && (
                <AudioPlayer
                  src={questionData.question.metadata.url}
                  title={questionData.question.metadata.keyword || 'Audio Content'}
                  className="bg-white/70 dark:bg-slate-800/70"
                />
              )}

              {/* Image Display from metadata URL */}
              {questionData.question.metadata?.url &&
               questionData.question.metadata?.content_type?.startsWith('image') && (
                <div className="flex flex-col items-center gap-3 p-4 bg-white/70 dark:bg-slate-800/70 rounded-lg">
                  <img
                    src={questionData.question.metadata.url}
                    alt="Question Image"
                    className="max-w-full max-h-64 object-contain rounded-lg border-2 border-blue-200 dark:border-blue-700 shadow-lg"
                    onError={() => {
                      console.log('Image failed to load:', questionData.question.metadata?.url)
                    }}
                  />
                  <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    {questionData.question.metadata.keyword || 'Identify this image'}
                  </span>
                </div>
              )}

              {/* Image Display from image_metadata */}
              {questionData.question.image_metadata?.url && (
                <div className="flex flex-col items-center gap-3 p-4 bg-white/70 dark:bg-slate-800/70 rounded-lg">
                  <img
                    src={questionData.question.image_metadata.url}
                    alt="Question Image"
                    className="max-w-full max-h-64 object-contain rounded-lg border-2 border-blue-200 dark:border-blue-700 shadow-lg"
                    onError={() => {
                      console.log('Image failed to load:', questionData.question.image_metadata?.url)
                    }}
                  />
                  <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Identify this image
                  </span>
                </div>
              )}

              {/* Fallback: Display image if URL doesn't have content_type but might be an image */}
              {questionData.question.metadata?.url &&
               !questionData.question.metadata?.content_type?.startsWith('audio') &&
               !questionData.question.metadata?.content_type?.startsWith('image') &&
               !questionData.question.image_metadata?.url && (
                <div className="space-y-3">
                  {/* Try to display as image first */}
                  <div className="flex flex-col items-center gap-3 p-4 bg-white/70 dark:bg-slate-800/70 rounded-lg">
                    <img
                      src={questionData.question.metadata.url}
                      alt="Question Content"
                      className="max-w-full max-h-64 object-contain rounded-lg border-2 border-blue-200 dark:border-blue-700 shadow-lg"
                      onError={(e) => {
                        // If image fails, hide it and show audio player instead
                        const target = e.target as HTMLImageElement
                        target.style.display = 'none'
                        const audioContainer = target.parentElement?.nextElementSibling as HTMLElement
                        if (audioContainer) {
                          audioContainer.style.display = 'block'
                        }
                      }}
                      onLoad={(e) => {
                        // If image loads successfully, hide the audio player
                        const target = e.target as HTMLImageElement
                        const audioContainer = target.parentElement?.nextElementSibling as HTMLElement
                        if (audioContainer) {
                          audioContainer.style.display = 'none'
                        }
                      }}
                    />
                    <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                      {questionData.question.metadata.keyword || 'Question Content'}
                    </span>
                  </div>

                  {/* Fallback audio player (hidden by default) */}
                  <div style={{ display: 'none' }}>
                    <AudioPlayer
                      src={questionData.question.metadata.url}
                      title={questionData.question.metadata.keyword || 'Audio Content'}
                      className="bg-white/70 dark:bg-slate-800/70"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Answer Hint */}
        <div>
          <div className="flex items-center gap-3 mb-1">
            <label className="text-sm font-medium text-foreground">
              Answer Hint
            </label>
            <div className="px-2 py-1 bg-blue-50 dark:bg-blue-950/30 rounded-md border border-blue-200/50 dark:border-blue-800/30">
              <p className="text-xs text-blue-700 dark:text-blue-300">
                💡 {questionData.type === 'image_identification'
                  ? 'Used to generate image'
                  : questionData.type === 'speak_word'
                  ? 'Used to generate audio'
                  : 'Helps users understand'}
              </p>
            </div>
          </div>
          <input
            type="text"
            value={formData.answer_hint}
            onChange={(e) => handleInputChange('answer_hint', e.target.value)}
            className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500/20 bg-background text-foreground text-sm"
            placeholder="Enter answer hint..."
          />
        </div>

        {/* Question Text */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
          <div>
            <label className="block text-sm font-medium text-foreground mb-1">
              Question Text (Original)
            </label>
            <textarea
              value={formData.text}
              onChange={(e) => handleInputChange('text', e.target.value)}
              className={cn(
                'w-full px-3 py-2 border rounded-md resize-none text-sm',
                'focus:outline-none focus:ring-2 focus:ring-blue-500/20',
                'bg-background text-foreground',
                errors.text ? 'border-red-500' : 'border-border'
              )}
              rows={1}
              style={{ minHeight: '38px', height: 'auto' }}
              onInput={(e) => {
                const target = e.target as HTMLTextAreaElement;
                target.style.height = 'auto';
                target.style.height = Math.max(38, target.scrollHeight) + 'px';
              }}
              placeholder="Enter question text..."
            />
            {errors.text && (
              <p className="text-red-500 text-xs mt-1">{errors.text}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-1">
              Translated Text (English)
            </label>
            <textarea
              value={formData.translated_text}
              onChange={(e) => handleInputChange('translated_text', e.target.value)}
              className={cn(
                'w-full px-3 py-2 border rounded-md resize-none text-sm',
                'focus:outline-none focus:ring-2 focus:ring-blue-500/20',
                'bg-background text-foreground',
                errors.translated_text ? 'border-red-500' : 'border-border'
              )}
              rows={1}
              style={{ minHeight: '38px', height: 'auto' }}
              onInput={(e) => {
                const target = e.target as HTMLTextAreaElement;
                target.style.height = 'auto';
                target.style.height = Math.max(38, target.scrollHeight) + 'px';
              }}
              placeholder="Enter translated text..."
            />
            {errors.translated_text && (
              <p className="text-red-500 text-xs mt-1">{errors.translated_text}</p>
            )}
          </div>
        </div>

        {/* Answer Type Display (Read-only) */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-lg p-3 border border-green-200/50 dark:border-green-800/30">
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-foreground">
              Answer Configuration
            </label>
            <div className="flex items-center gap-2 px-2 py-1 bg-green-100 dark:bg-green-900/30 rounded-full">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs font-medium text-green-700 dark:text-green-300">
                {formData.correct_answer_type === 'single' ? 'Single Choice' : 'Multiple Choice'}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
            <div className="space-y-1">
              <span className="text-muted-foreground text-xs">Answer Type:</span>
              <div className="font-medium text-foreground text-sm">
                {questionData.correct_answer?.type || 'single_choice'}
              </div>
            </div>
            <div className="space-y-1">
              <span className="text-muted-foreground text-xs">Current Value:</span>
              <div className="font-medium text-foreground text-sm">
                {getCurrentValue() || 'No selection'}
              </div>
            </div>
          </div>

          <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-950/30 rounded border border-blue-200/50 dark:border-blue-800/30">
            <p className="text-xs text-blue-700 dark:text-blue-300">
              💡 Answer type is determined by the original question format and cannot be changed.
              You can only modify which options are marked as correct.
            </p>
          </div>
        </div>

        {/* Options */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-foreground">
              Answer Options
            </label>
            <button
              type="button"
              onClick={addOption}
              className="flex items-center gap-1 px-2 py-1 text-xs bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors"
            >
              <Plus className="w-3 h-3" />
              Add Option
            </button>
          </div>
          <div className="space-y-3">
            {optionKeys.map((key, index) => {
              const optionAudio = questionData.question.options_metadata?.[key]?.audio_url
              const isSelected = selectedAnswers.includes(index)

              return (
                <motion.div
                  key={key}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={cn(
                    'p-3 rounded-md space-y-2 border-2 transition-all duration-200',
                    isSelected
                      ? 'bg-blue-50 dark:bg-blue-950/30 border-blue-300 dark:border-blue-700'
                      : 'bg-slate-50 dark:bg-slate-800/50 border-slate-200 dark:border-slate-700'
                  )}
                >
                  {/* Option Header with Selection and Audio */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <input
                        type={formData.correct_answer_type === 'single' ? 'radio' : 'checkbox'}
                        name={formData.correct_answer_type === 'single' ? 'correct_answer' : `correct_answer_${key}`}
                        checked={isSelected}
                        onChange={() => handleAnswerSelection(index)}
                        className="text-blue-600"
                      />
                      <span className="text-sm font-medium text-foreground">
                        Option {key.toUpperCase()}
                      </span>
                      {isSelected && (
                        <div className="flex items-center gap-1 px-1.5 py-0.5 text-xs bg-green-500 text-white rounded-full">
                          <CheckCircle className="w-3 h-3" />
                          <span>Correct</span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      {optionAudio && (
                        <AudioPlayer
                          src={optionAudio}
                          title={`Option ${key.toUpperCase()}`}
                          className={cn(
                            isSelected
                              ? 'bg-blue-50/70 dark:bg-blue-950/50'
                              : 'bg-slate-50/70 dark:bg-slate-800/70'
                          )}
                        />
                      )}

                      {optionKeys.length > 2 && (
                        <button
                          type="button"
                          onClick={() => removeOption(key)}
                          className="p-1 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-md transition-colors"
                          title="Remove option"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Option Text Fields */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                    <div>
                      <label className="block text-xs font-medium text-muted-foreground mb-1">
                        Original Text
                      </label>
                      <input
                        type="text"
                        value={formData.options[key] || ''}
                        onChange={(e) => handleOptionChange(key, e.target.value)}
                        className={cn(
                          'w-full px-2 py-1.5 border rounded-md text-sm transition-all duration-200',
                          'focus:outline-none focus:ring-2 focus:ring-blue-500/20',
                          'bg-background text-foreground',
                          errors[`option_${key}`] ? 'border-red-500' : 'border-border',
                          isSelected && 'ring-2 ring-blue-500/20'
                        )}
                        placeholder={`Option ${key.toUpperCase()}`}
                      />
                      {errors[`option_${key}`] && (
                        <p className="text-red-500 text-xs mt-1">{errors[`option_${key}`]}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-muted-foreground mb-1">
                        English Text
                      </label>
                      <input
                        type="text"
                        value={formData.options_en[key] || ''}
                        onChange={(e) => handleOptionChange(key, e.target.value, true)}
                        className={cn(
                          'w-full px-2 py-1.5 border rounded-md text-sm transition-all duration-200',
                          'focus:outline-none focus:ring-2 focus:ring-blue-500/20',
                          'bg-background text-foreground',
                          errors[`option_en_${key}`] ? 'border-red-500' : 'border-border',
                          isSelected && 'ring-2 ring-blue-500/20'
                        )}
                        placeholder={`English option ${key.toUpperCase()}`}
                      />
                      {errors[`option_en_${key}`] && (
                        <p className="text-red-500 text-xs mt-1">{errors[`option_en_${key}`]}</p>
                      )}
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>



        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-3 border-t border-border">
          <div className="flex items-center gap-2">
            <button
              type="button"
              onClick={onCancel}
              disabled={saving}
              className="px-4 py-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleReset}
              disabled={saving}
              className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors disabled:opacity-50"
            >
              <RotateCcw className="w-4 h-4" />
              Reset
            </button>
          </div>
          <button
            type="submit"
            disabled={saving || !hasChanges()}
            className={cn(
              'flex items-center gap-2 px-6 py-2 rounded-lg font-medium text-white',
              'bg-gradient-to-r from-blue-500 to-blue-600',
              'hover:from-blue-600 hover:to-blue-700 transition-all duration-200',
              'focus:outline-none focus:ring-2 focus:ring-blue-500/20',
              'disabled:opacity-50 disabled:cursor-not-allowed'
            )}
          >
            {saving ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Updating...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>Update</span>
              </>
            )}
          </button>
        </div>
      </form>
    </motion.div>
  )
}

export default QuestionEditForm
