import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { X, Save, Loader2 } from 'lucide-react'
import {
  DailyQuestion,
  Theme,
  CreateDailyQuestionRequest,
  UpdateDailyQuestionRequest,
  createDailyQuestion,
  updateDailyQuestion
} from '../../../services/curatedService'

interface DailyQuestionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  question?: DailyQuestion | null
  themes: Theme[]
  onSuccess: () => void
  loading?: boolean
}

/**
 * Modal for creating and editing daily questions
 */
const DailyQuestionModal: React.FC<DailyQuestionModalProps> = ({
  open,
  onOpenChange,
  question,
  themes,
  onSuccess,
  loading: externalLoading = false
}) => {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    theme_id: '',
    text: '',
    text_en: ''
  })
  const [originalData, setOriginalData] = useState({
    theme_id: '',
    text: '',
    text_en: ''
  })

  const isEditing = !!question

  // Reset form when modal opens/closes or question changes
  useEffect(() => {
    if (open) {
      if (question) {
        const initialData = {
          theme_id: question.theme_id || '',
          text: question.text || '',
          text_en: question.text_en || ''
        }
        setFormData({ ...initialData })
        setOriginalData({ ...initialData })
      } else {
        const initialData = {
          theme_id: '',
          text: '',
          text_en: ''
        }
        setFormData({ ...initialData })
        setOriginalData({ ...initialData })
      }
    }
  }, [open, question])

  // Handle form field changes
  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Check if there are changes from original data
  const hasChanges = () => {
    if (!isEditing) return true // For new questions, always allow submission

    const textChanged = formData.text.trim() !== originalData.text.trim()
    const textEnChanged = formData.text_en.trim() !== originalData.text_en.trim()
    const themeChanged = formData.theme_id !== originalData.theme_id

    console.log('Change detection:', {
      isEditing,
      textChanged,
      textEnChanged,
      themeChanged,
      formDataText: formData.text,
      originalDataText: originalData.text,
      formDataTextEn: formData.text_en,
      originalDataTextEn: originalData.text_en
    })

    return textChanged || textEnChanged || themeChanged
  }

  // Validate form
  const isFormValid = () => {
    const hasText = formData.text.trim().length > 0
    const hasTextEn = formData.text_en.trim().length > 0
    const hasTheme = formData.theme_id.length > 0

    // For new questions, all fields are required
    if (!isEditing) {
      return hasTheme && hasText && hasTextEn
    }

    // For editing, at least one text field must have content
    // (theme can be unchanged, and not all questions need both languages)
    return hasText || hasTextEn
  }

  // Check if form can be submitted (valid and has changes)
  const canSubmit = () => {
    const valid = isFormValid()
    const changes = hasChanges()
    const result = valid && changes

    console.log('canSubmit:', { valid, changes, result })

    return result
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!canSubmit()) {
      if (!isFormValid()) {
        if (!isEditing && !formData.theme_id) {
          alert('Please select a theme')
        } else if (!isEditing && (!formData.text.trim() || !formData.text_en.trim())) {
          alert('Please enter both question texts for new questions')
        } else if (isEditing && !formData.text.trim() && !formData.text_en.trim()) {
          alert('Please enter at least one question text')
        } else {
          alert('Please fill in all required fields')
        }
      } else if (isEditing && !hasChanges()) {
        alert('No changes detected. Please make changes before updating.')
      }
      return
    }

    try {
      setLoading(true)

      if (isEditing && question) {
        // Update existing question
        console.log('Editing question:', question)
        console.log('Question _id:', question._id)
        console.log('Question id:', question.id)

        const updateData: UpdateDailyQuestionRequest = {
          text: formData.text.trim(),
          text_en: formData.text_en.trim()
        }

        // Only include theme_id if it has changed
        if (formData.theme_id !== question.theme_id) {
          updateData.theme_id = formData.theme_id
        }

        const questionId = question._id || question.id
        console.log('Using question ID:', questionId)

        if (!questionId) {
          throw new Error('Question ID is missing')
        }

        await updateDailyQuestion(questionId, updateData)
        console.log('Question updated successfully')
      } else {
        // Create new question
        const createData: CreateDailyQuestionRequest = {
          theme_id: formData.theme_id,
          text: formData.text.trim(),
          text_en: formData.text_en.trim()
        }

        await createDailyQuestion(createData)
        console.log('Question created successfully')
      }

      onSuccess()
    } catch (error: any) {
      console.error('Failed to save question:', error)
      alert(error.message || 'Failed to save question')
    } finally {
      setLoading(false)
    }
  }

  // Get selected theme for display - use theme.id to match the form value
  const selectedTheme = themes.find(theme => theme.id === formData.theme_id)

  if (!open) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={() => onOpenChange(false)}
      />

      {/* Modal */}
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="relative bg-card border border-border rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-lg">
              <Save className="w-4 h-4 text-white" />
            </div>
            <h2 className="text-xl font-semibold text-foreground">
              {isEditing ? 'Edit Daily Question' : 'Create Daily Question'}
            </h2>
          </div>
          <button
            onClick={() => onOpenChange(false)}
            className="p-2 text-muted-foreground hover:text-foreground transition-colors rounded-lg hover:bg-accent"
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        {/* Form Content */}
        <div className="flex-1 overflow-y-auto">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Theme Selection */}
            <div className="space-y-3">
              <label htmlFor="theme_id" className="text-sm font-semibold text-foreground">
                Theme <span className="text-destructive">*</span>
              </label>
              <select
                id="theme_id"
                value={formData.theme_id}
                onChange={(e) => handleChange('theme_id', e.target.value)}
                className="w-full px-4 py-3 border border-border rounded-lg text-sm bg-background text-foreground focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
              >
              <option value="">Select a theme</option>
              {themes.map((theme) => (
                <option key={theme._id} value={theme.id}>
                  {theme.icon} {theme.name_en} ({theme.category})
                </option>
              ))}
            </select>
            {selectedTheme && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 rounded-xl border border-border bg-accent/50"
              >
                <div className="flex items-center gap-3 text-sm">
                  <span className="text-2xl">{selectedTheme.icon}</span>
                  <div className="flex-1">
                    <div className="font-semibold text-foreground">{selectedTheme.name_en}</div>
                    <div className="text-xs text-muted-foreground">{selectedTheme.description_en}</div>
                  </div>
                  <span
                    className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border"
                    style={{
                      backgroundColor: `${selectedTheme.background_color}20`,
                      color: selectedTheme.font_color,
                      borderColor: `${selectedTheme.background_color}40`
                    }}
                  >
                    {selectedTheme.category}
                  </span>
                </div>
              </motion.div>
            )}
          </div>

          {/* Question Text (Primary Language) */}
          <div className="space-y-3">
            <label htmlFor="text" className="text-sm font-semibold text-foreground">
              Question Text <span className="text-destructive">*</span>
            </label>
            <textarea
              id="text"
              value={formData.text}
              onChange={(e) => handleChange('text', e.target.value)}
              placeholder="Enter the question in the primary language..."
              rows={4}
              className="w-full px-4 py-3 border border-border rounded-lg text-sm bg-background text-foreground resize-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
            />
            <p className="text-xs text-muted-foreground">
              {formData.text.length}/500 characters
            </p>
          </div>

          {/* Question Text (English) */}
          <div className="space-y-3">
            <label htmlFor="text_en" className="text-sm font-semibold text-foreground">
              Question Text (English) <span className="text-destructive">*</span>
            </label>
            <textarea
              id="text_en"
              value={formData.text_en}
              onChange={(e) => handleChange('text_en', e.target.value)}
              placeholder="Enter the question in English..."
              rows={4}
              className="w-full px-4 py-3 border border-border rounded-lg text-sm bg-background text-foreground resize-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
            />
            <p className="text-xs text-muted-foreground">
              {formData.text_en.length}/500 characters
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-6 border-t border-border">
            <button
              type="button"
              onClick={() => onOpenChange(false)}
              disabled={loading || externalLoading}
              className="inline-flex items-center px-6 py-3 border border-border rounded-lg text-sm font-medium text-foreground bg-background hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!canSubmit() || loading || externalLoading}
              className="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading || externalLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {isEditing ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditing ? 'Update Question' : 'Create Question'}
                </>
              )}
            </button>
          </div>
          </form>
        </div>
      </motion.div>
    </div>
  )
}

export default DailyQuestionModal
